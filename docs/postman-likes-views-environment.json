{"id": "likes-views-environment", "name": "Likes & Views Environment", "values": [{"key": "base_url", "value": "http://localhost:8000/api", "description": "URL de base de l'API Laravel", "enabled": true, "type": "default"}, {"key": "auth_token", "value": "", "description": "Token d'authentification Sanctum (à remplir après génération)", "enabled": true, "type": "secret"}, {"key": "user_email", "value": "<EMAIL>", "description": "Email de l'utilisateur de test", "enabled": true, "type": "default"}, {"key": "user_password", "value": "password", "description": "Mot de passe de l'utilisateur de test", "enabled": true, "type": "secret"}, {"key": "professional_profile_id", "value": "1", "description": "ID du profil professionnel à tester", "enabled": true, "type": "default"}, {"key": "server_url", "value": "http://localhost:8000", "description": "URL du serveur Laravel (sans /api)", "enabled": true, "type": "default"}], "_postman_variable_scope": "environment"}