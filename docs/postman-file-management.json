{"info": {"name": "Hi3D - File Management System", "description": "Collection pour tester le système de gestion de fichiers avec SwissTransfer", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:8000/api", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}, {"key": "file_id", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/login", "host": ["{{base_url}}"], "path": ["login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('auth_token', response.data.token);", "    pm.test('Login successful', function () {", "        pm.expect(response.success).to.be.true;", "    });", "} else {", "    pm.test('<PERSON><PERSON> failed', function () {", "        pm.expect.fail('<PERSON><PERSON> request failed');", "    });", "}"]}}]}]}, {"name": "File Upload", "item": [{"name": "Upload Single File (Small - Local Storage)", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files[]", "type": "file", "src": "/path/to/small-file.jpg", "description": "Fichier < 10MB pour stockage local"}]}, "url": {"raw": "{{base_url}}/files/upload", "host": ["{{base_url}}"], "path": ["files", "upload"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Upload successful', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data.storage_type).to.eql('local');", "    pm.collectionVariables.set('file_id', response.data.id);", "});"]}}]}, {"name": "Upload Single File (Large - SwissTransfer)", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files[]", "type": "file", "src": "/path/to/large-file.zip", "description": "Fichier > 10MB pour SwissTransfer"}, {"key": "options[message]", "value": "Fichier partagé via Hi3D", "type": "text"}, {"key": "options[expiration_days]", "value": "30", "type": "text"}]}, "url": {"raw": "{{base_url}}/files/upload", "host": ["{{base_url}}"], "path": ["files", "upload"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Upload successful', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data.storage_type).to.eql('swisstransfer');", "    pm.expect(response.data.expires_at).to.not.be.null;", "});"]}}]}, {"name": "Upload Multiple Files", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files[]", "type": "file", "src": "/path/to/file1.jpg"}, {"key": "files[]", "type": "file", "src": "/path/to/file2.pdf"}, {"key": "files[]", "type": "file", "src": "/path/to/file3.docx"}]}, "url": {"raw": "{{base_url}}/files/upload", "host": ["{{base_url}}"], "path": ["files", "upload"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Multiple upload successful', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data.files).to.be.an('array');", "    pm.expect(response.data.statistics.total).to.be.above(0);", "});"]}}]}, {"name": "Upload with Fileable Relation", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files[]", "type": "file", "src": "/path/to/achievement-file.jpg"}, {"key": "fileable_type", "value": "App\\Models\\Achievement", "type": "text"}, {"key": "fileable_id", "value": "1", "type": "text"}]}, "url": {"raw": "{{base_url}}/files/upload", "host": ["{{base_url}}"], "path": ["files", "upload"]}}}]}, {"name": "File Management", "item": [{"name": "List User Files", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/files?per_page=20&status=completed", "host": ["{{base_url}}"], "path": ["files"], "query": [{"key": "per_page", "value": "20"}, {"key": "status", "value": "completed"}, {"key": "storage_type", "value": "local", "disabled": true}, {"key": "fileable_type", "value": "App\\Models\\Achievement", "disabled": true}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Files list retrieved', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data.files).to.be.an('array');", "    pm.expect(response.data.pagination).to.be.an('object');", "});"]}}]}, {"name": "Get File Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/files/{{file_id}}", "host": ["{{base_url}}"], "path": ["files", "{{file_id}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('File details retrieved', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data.id).to.not.be.undefined;", "    pm.expect(response.data.original_name).to.not.be.undefined;", "});"]}}]}, {"name": "Get Download URL", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/files/{{file_id}}/download", "host": ["{{base_url}}"], "path": ["files", "{{file_id}}", "download"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Download URL retrieved', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data.download_url).to.not.be.undefined;", "});"]}}]}, {"name": "Delete File", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/files/{{file_id}}", "host": ["{{base_url}}"], "path": ["files", "{{file_id}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('File deleted successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "});"]}}]}]}, {"name": "Admin Functions", "item": [{"name": "Get Storage Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/files/admin/stats", "host": ["{{base_url}}"], "path": ["files", "admin", "stats"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Statistics retrieved', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data.total_files).to.be.a('number');", "    pm.expect(response.data.local_files).to.be.a('number');", "    pm.expect(response.data.swisstransfer_files).to.be.a('number');", "});"]}}]}]}, {"name": "Erro<PERSON>", "item": [{"name": "Upload Invalid File Type", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files[]", "type": "file", "src": "/path/to/malicious.exe", "description": "Fichier avec extension non autorisée"}]}, "url": {"raw": "{{base_url}}/files/upload", "host": ["{{base_url}}"], "path": ["files", "upload"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Invalid file type rejected', function () {", "    pm.response.to.have.status(500);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.false;", "});"]}}]}, {"name": "Upload Too Large File", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files[]", "type": "file", "src": "/path/to/very-large-file.zip", "description": "Fichier dépassant FILE_MAX_UPLOAD_SIZE"}]}, "url": {"raw": "{{base_url}}/files/upload", "host": ["{{base_url}}"], "path": ["files", "upload"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Large file rejected', function () {", "    pm.response.to.have.status(422);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.false;", "});"]}}]}, {"name": "Access File Without Permission", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer invalid_token"}], "url": {"raw": "{{base_url}}/files/999", "host": ["{{base_url}}"], "path": ["files", "999"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Unauthorized access denied', function () {", "    pm.response.to.have.status(401);", "});"]}}]}]}]}