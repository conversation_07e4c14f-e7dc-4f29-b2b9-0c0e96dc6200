{"info": {"name": "Likes & Views API Tests", "description": "Collection complète pour tester les fonctionnalités de likes et vues des profils professionnels", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "variable": [{"key": "base_url", "value": "http://localhost:8000/api", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}, {"key": "professional_profile_id", "value": "1", "type": "string"}], "item": [{"name": "Views (Public APIs)", "item": [{"name": "Record View", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/professionals/{{professional_profile_id}}/view", "host": ["{{base_url}}"], "path": ["professionals", "{{professional_profile_id}}", "view"]}}, "response": []}, {"name": "Get View Stats", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/professionals/{{professional_profile_id}}/view/stats", "host": ["{{base_url}}"], "path": ["professionals", "{{professional_profile_id}}", "view", "stats"]}}, "response": []}, {"name": "Check View Status", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/professionals/{{professional_profile_id}}/view/status", "host": ["{{base_url}}"], "path": ["professionals", "{{professional_profile_id}}", "view", "status"]}}, "response": []}]}, {"name": "Likes (Protected APIs)", "item": [{"name": "Like Profile", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/professionals/{{professional_profile_id}}/like", "host": ["{{base_url}}"], "path": ["professionals", "{{professional_profile_id}}", "like"]}}, "response": []}, {"name": "Unlike Profile", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/professionals/{{professional_profile_id}}/like", "host": ["{{base_url}}"], "path": ["professionals", "{{professional_profile_id}}", "like"]}}, "response": []}, {"name": "Toggle Like", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/professionals/{{professional_profile_id}}/like/toggle", "host": ["{{base_url}}"], "path": ["professionals", "{{professional_profile_id}}", "like", "toggle"]}}, "response": []}, {"name": "Check Like Status", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/professionals/{{professional_profile_id}}/like/status", "host": ["{{base_url}}"], "path": ["professionals", "{{professional_profile_id}}", "like", "status"]}}, "response": []}]}, {"name": "Authentication", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/health-check", "host": ["{{base_url}}"], "path": ["health-check"]}}, "response": []}]}, {"name": "Security Tests", "item": [{"name": "Like without Auth (Should Fail)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/professionals/{{professional_profile_id}}/like", "host": ["{{base_url}}"], "path": ["professionals", "{{professional_profile_id}}", "like"]}}, "response": []}, {"name": "Like with <PERSON><PERSON><PERSON> (Should Fail)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer invalid-token-here"}], "url": {"raw": "{{base_url}}/professionals/{{professional_profile_id}}/like", "host": ["{{base_url}}"], "path": ["professionals", "{{professional_profile_id}}", "like"]}}, "response": []}, {"name": "Like Non-existent Profile (Should Fail)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/professionals/99999/like", "host": ["{{base_url}}"], "path": ["professionals", "99999", "like"]}}, "response": []}]}]}