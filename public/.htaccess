<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    # CORS is handled by Laravel middleware, not Apache
    # Commented out to avoid conflicts with Laravel CORS configuration
    # <IfModule mod_headers.c>
    #     # Récupérer l'origine de la requête
    #     SetEnvIf Origin "^(.*)$" ORIGIN=$1
    #
    #     # Définir l'en-tête Access-Control-Allow-Origin avec l'origine de la requête
    #     Header always set Access-Control-Allow-Origin "%{ORIGIN}e" env=ORIGIN
    #     Header always set Access-Control-Allow-Methods "GET, POST, PUT, PATCH, DELETE, OPTIONS"
    #     Header always set Access-Control-Allow-Headers "Authorization, Content-Type, X-Requested-With, X-XSRF-TOKEN"
    #     Header always set Access-Control-Allow-Credentials "true"
    #
    #     # Handle OPTIONS method
    #     RewriteEngine On
    #     RewriteCond %{REQUEST_METHOD} OPTIONS
    #     RewriteRule ^(.*)$ $1 [R=200,L]
    # </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>
